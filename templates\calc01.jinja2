{% extends "base.jinja2" %}

{% block title %}Calc01{% endblock %}
{% block content %}
  <div id="content-div">

    {# TITLE SINGLE #}
    <div>
      <style>
          me {
            width: 100%;
            text-align: center;
            padding-bottom: 8px;
          }

          me div {
            padding-bottom: 14px;
            font-family: 'Noto Serif', serif;
            font-size: 24px;
            font-weight: 400;
            font-stretch: semi-condensed;
            font-style: italic;
          }

          me hr {
            display: block;
            height: 1px;
            border: 0;
            border-top: 1px solid #a8a8a8;
            margin: 0;
            padding: 0;
          }
      </style>
      <div>{{ user.email }}</div>
      <hr />
    </div>

    <form data-signals="{_users_submit_button_disable:false}" data-on-submit="$_users_submit_button_disable = true;@post('/login_validate', {contentType: 'form'})">

      {{ render_partial('partials/forms-select-i.jinja2', titledisabled='Select Customer', optionslist=customer_names, infotext='The info of the selected customer will be showed on the pdf report.') }}

      {{ render_partial('partials/forms-select.jinja2', titledisabled='CBOD<sub>5</sub>', optionslist=['option1', 'option2', 'option3']) }}

      {{ render_partial('partials/forms-input.jinja2', namealwayschange='firstcalc02', fieldtext='CBOD<sub>5</sub>', type = 'text', pattern="[\s\S]*", errormessage="Pattern not satisfied") }}

      {{ render_partial('partials/forms-input-i.jinja2', namealwayschange='firstcalc03', fieldtext='CO<sup>2</sup> levels', type = 'text', pattern="[\s\S]*", errormessage="Pattern not satisfied", infotext='The info of the selected customer will be showed on the pdf report.' ) }}
      {{ render_partial('partials/forms-input-i.jinja2', namealwayschange='firstcalc03', fieldtext='CO<sup>2</sup> levels', type = 'text', pattern="[\s\S]*", errormessage="Pattern not satisfied", infotext=render_partial("partials/forms-info-popover-content.jinja2") ) }}
    </form>

  </div>
{% endblock content %}