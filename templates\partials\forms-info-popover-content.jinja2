<div>
  <style>
    me {
      all: unset;
      width: 400px;
      font-family: sans-serif;
      border: 2px solid #f60a0a;
      display: block;
    }

    me .header {
      background-color: #964B00;
      color: var(--color-text-bright);
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 20px;
    }

    me .unit-row {
      background-color: #2E0064;
      color: white;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px;
      font-weight: bold;
    }

    me .content {
      background-color: #e8e6d1;
      padding: 15px 10px;
      text-align: center;
      font-size: 15px;
    }

    me .range-row {
      background-color: #007300;
      color: white;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px;
      font-weight: bold;
    }
  </style>

  <div class="header">Total Primary Tank Volume</div>
  <div class="unit-row">
    <span class="left">Unit</span>
    <span class="right">L/PE/d</span>
  </div>
  <div class="content">
    This was indeed the original design; it will work well up until you begin receiving subtrees (ex: DOM swaps with htmx, ajax, jquery, etc.) which requires walking all subtree elements to ensure we do not miss a style.
    <br>
    <br>
    This was indeed the original design; it will work well up until you begin receiving subtrees (ex: DOM swaps with htmx, ajax, jquery, etc.) which requires walking all subtree elements to ensure we do not miss a style.
  </div>
  <div class="range-row">
    <span class="left">Range</span>
    <span class="right">0.0-0.333</span>
  </div>
</div>